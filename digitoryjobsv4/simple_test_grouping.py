#!/usr/bin/env python3
"""
Simple test script for inventory consumption grouping by category and subcategory
"""

import pandas as pd

def test_inventory_grouping():
    """Test grouping inventory data by category and subcategory"""
    
    # Load the CSV file
    print("Loading inventory_consumption.csv...")
    df = pd.read_csv('inventory_consumption.csv')
    print(f"Loaded {len(df)} records")
    
    # Display basic info
    print(f"\nColumns: {list(df.columns)}")
    print(f"Categories: {df['Category'].unique()}")
    print(f"Sample Sub Categories: {df['Sub Category'].unique()[:10]}")
    
    # Define numeric columns to sum
    numeric_cols = [
        'WorkArea Opening', 'WorkArea Indent', 'WorkArea Transfer In',
        'WorkArea Transfer Out', 'Return To Store Out', 'Spoilage/Adjustments',
        'Total WorkArea Stock', 'WorkArea Closing', 'Actual', 'Theoretical',
        'Variance Qty', 'Actual(incl.tax,etc)', 'Theoretical(incl.tax,etc)',
        'Variance (incl.tax,etc)'
    ]
    
    # Convert to numeric and fill NaN with 0
    for col in numeric_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    
    # Group by Category and Sub Category, then sum
    print("\nGrouping by Category and Sub Category...")
    grouped = df.groupby(['Category', 'Sub Category'])[numeric_cols].sum().reset_index()
    
    print(f"Created {len(grouped)} category-subcategory groups")
    
    # Display results
    print("\nTop 10 groups by Actual consumption value:")
    top_groups = grouped.nlargest(10, 'Actual(incl.tax,etc)')
    print(top_groups[['Category', 'Sub Category', 'Actual', 'Actual(incl.tax,etc)']].to_string(index=False))
    
    # Save results
    grouped.to_csv('inventory_grouped_simple.csv', index=False)
    print(f"\nResults saved to inventory_grouped_simple.csv")
    
    return grouped

if __name__ == "__main__":
    result = test_inventory_grouping()