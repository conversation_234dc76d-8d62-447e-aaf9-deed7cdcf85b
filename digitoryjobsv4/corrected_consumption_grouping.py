#!/usr/bin/env python3
"""
CORRECTED: Inventory consumption grouping script that shows ACTUAL CONSUMPTION
This script groups by Category and Sub Category and sums the ACTUAL CONSUMPTION values
"""

import pandas as pd

def correct_consumption_grouping():
    """Group inventory data by category and subcategory showing ACTUAL CONSUMPTION"""
    
    print("CORRECTED INVENTORY CONSUMPTION GROUPING")
    print("="*80)
    
    # Load the CSV file
    df = pd.read_csv('inventory_consumption.csv')
    print(f"Loaded {len(df)} records")
    
    # Display basic info
    print(f"\nColumns: {list(df.columns)}")
    print(f"Categories: {df['Category'].unique()}")
    
    # Define the CORRECT column for consumption
    consumption_column = 'Actual(incl.tax,etc)'
    
    # Convert to numeric and fill NaN with 0
    df[consumption_column] = pd.to_numeric(df[consumption_column], errors='coerce').fillna(0)
    
    # Group by Category and Sub Category, then sum ACTUAL CONSUMPTION
    print(f"\nGrouping by Category and Sub Category using '{consumption_column}' column...")
    grouped = df.groupby(['Category', 'Sub Category'])[consumption_column].sum().reset_index()
    grouped.columns = ['Category', 'Sub Category', 'Actual_Consumption_Value']
    
    print(f"Created {len(grouped)} category-subcategory groups")
    
    # Display results sorted by consumption value
    print("\nTop 15 groups by ACTUAL CONSUMPTION value:")
    top_groups = grouped.nlargest(15, 'Actual_Consumption_Value')
    print(top_groups.to_string(index=False))
    
    # Check GROCERY specifically
    grocery_row = grouped[grouped['Sub Category'] == 'GROCERY']
    if not grocery_row.empty:
        grocery_value = grocery_row['Actual_Consumption_Value'].iloc[0]
        print(f"\n" + "="*80)
        print(f"GROCERY VERIFICATION:")
        print(f"GROCERY Actual Consumption: ₹{grocery_value:,.2f}")
        print(f"This should match the dashboard consumption value")
        print("="*80)
    
    # Save results
    grouped.to_csv('inventory_consumption_corrected.csv', index=False)
    print(f"\nCorrected results saved to inventory_consumption_corrected.csv")
    
    # Summary by Category
    print(f"\n" + "="*50)
    print("SUMMARY BY CATEGORY:")
    print("="*50)
    category_summary = grouped.groupby('Category')['Actual_Consumption_Value'].sum().sort_values(ascending=False)
    for category, value in category_summary.items():
        print(f"{category:20} = ₹{value:15,.2f}")
    
    return grouped

def compare_with_dashboard():
    """Compare our corrected values with what dashboard should show"""
    
    print(f"\n" + "="*80)
    print("DASHBOARD RECONCILIATION CHECK")
    print("="*80)
    
    # Load the CSV file
    df = pd.read_csv('inventory_consumption.csv')
    
    # Get GROCERY data
    grocery_df = df[df['Sub Category'] == 'GROCERY'].copy()
    
    if not grocery_df.empty:
        # Convert to numeric
        for col in ['Actual(incl.tax,etc)', 'WorkArea Opening(incl.tax,etc)']:
            grocery_df[col] = pd.to_numeric(grocery_df[col], errors='coerce').fillna(0)
        
        actual_consumption = grocery_df['Actual(incl.tax,etc)'].sum()
        opening_stock = grocery_df['WorkArea Opening(incl.tax,etc)'].sum()
        
        print(f"GROCERY Analysis:")
        print(f"• Actual Consumption (correct):     ₹{actual_consumption:15,.2f}")
        print(f"• Opening Stock (incorrect):        ₹{opening_stock:15,.2f}")
        print(f"• Dashboard currently shows:        ₹{47372:15,.2f}")
        print(f"• Grouped script was showing:       ₹{opening_stock:15,.2f}")
        
        print(f"\nRECOMMENDATION:")
        print(f"• Dashboard should show:            ₹{actual_consumption:15,.2f}")
        print(f"• Grouping script should show:      ₹{actual_consumption:15,.2f}")
        print(f"• Both should use 'Actual(incl.tax,etc)' column for consumption")

if __name__ == "__main__":
    result = correct_consumption_grouping()
    compare_with_dashboard()