#!/usr/bin/env python3
"""
Test script for inventory consumption data analysis
Groups data by Category and Sub Category and calculates sums
"""

import pandas as pd
import numpy as np
from pathlib import Path
import sys

def load_inventory_data(file_path):
    """Load inventory consumption CSV data"""
    try:
        df = pd.read_csv(file_path)
        print(f"✓ Successfully loaded {len(df)} records from {file_path}")
        print(f"✓ Columns: {list(df.columns)}")
        return df
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return None

def clean_numeric_columns(df):
    """Clean and convert numeric columns"""
    # Define numeric columns that should be summed
    numeric_columns = [
        'WorkArea Opening', 'WorkArea Indent', 'WorkArea Transfer In',
        'WorkArea Transfer Out', 'Return To Store Out', 'Spoilage/Adjustments',
        'Total WorkArea Stock', 'WorkArea Closing', 'Actual', 'Theoretical',
        'Variance Qty', 'WAC(incl.tax,etc)', 'WorkArea Opening(incl.tax,etc)',
        'WorkArea Closing(incl.tax,etc)', 'Actual(incl.tax,etc)',
        'Theoretical(incl.tax,etc)', 'Variance (incl.tax,etc)'
    ]
    
    # Convert to numeric, replacing any non-numeric values with 0
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    
    return df, numeric_columns

def group_and_sum_data(df, numeric_columns):
    """Group by Category and Sub Category, then sum numeric columns"""
    
    # Group by Category and Sub Category
    grouped = df.groupby(['Category', 'Sub Category'])[numeric_columns].sum().reset_index()
    
    # Sort by Category and Sub Category for better readability
    grouped = grouped.sort_values(['Category', 'Sub Category'])
    
    return grouped

def display_summary_stats(df, grouped_df):
    """Display summary statistics"""
    print("\n" + "="*80)
    print("SUMMARY STATISTICS")
    print("="*80)
    
    print(f"Total Records: {len(df)}")
    print(f"Unique Categories: {df['Category'].nunique()}")
    print(f"Unique Sub Categories: {df['Sub Category'].nunique()}")
    print(f"Unique Locations: {df['Location'].nunique()}")
    print(f"Unique Work Areas: {df['WorkArea'].nunique()}")
    
    print(f"\nAfter Grouping:")
    print(f"Category-SubCategory Groups: {len(grouped_df)}")

def display_category_breakdown(grouped_df):
    """Display breakdown by category"""
    print("\n" + "="*80)
    print("CATEGORY BREAKDOWN")
    print("="*80)
    
    category_summary = grouped_df.groupby('Category').agg({
        'Sub Category': 'count',
        'Actual': 'sum',
        'Theoretical': 'sum',
        'Variance Qty': 'sum',
        'Actual(incl.tax,etc)': 'sum',
        'Theoretical(incl.tax,etc)': 'sum',
        'Variance (incl.tax,etc)': 'sum'
    }).round(2)
    
    category_summary.columns = ['SubCategories', 'Total_Actual', 'Total_Theoretical', 
                               'Total_Variance_Qty', 'Total_Actual_Value', 
                               'Total_Theoretical_Value', 'Total_Variance_Value']
    
    print(category_summary)

def display_top_subcategories(grouped_df, top_n=10):
    """Display top subcategories by actual consumption value"""
    print(f"\n" + "="*80)
    print(f"TOP {top_n} SUBCATEGORIES BY ACTUAL CONSUMPTION VALUE")
    print("="*80)
    
    top_subcategories = grouped_df.nlargest(top_n, 'Actual(incl.tax,etc)')[
        ['Category', 'Sub Category', 'Actual', 'Actual(incl.tax,etc)', 'Variance (incl.tax,etc)']
    ].round(2)
    
    print(top_subcategories.to_string(index=False))

def save_grouped_data(grouped_df, output_file):
    """Save grouped data to CSV"""
    try:
        grouped_df.to_csv(output_file, index=False)
        print(f"\n✓ Grouped data saved to: {output_file}")
    except Exception as e:
        print(f"\n✗ Error saving data: {e}")

def main():
    """Main function to run the test script"""
    print("INVENTORY CONSUMPTION ANALYSIS - CATEGORY & SUBCATEGORY GROUPING")
    print("="*80)
    
    # File paths
    input_file = Path("inventory_consumption.csv")
    output_file = Path("inventory_consumption_grouped.csv")
    
    # Check if input file exists
    if not input_file.exists():
        print(f"✗ Input file not found: {input_file}")
        sys.exit(1)
    
    # Load data
    df = load_inventory_data(input_file)
    if df is None:
        sys.exit(1)
    
    # Clean numeric columns
    df, numeric_columns = clean_numeric_columns(df)
    print(f"✓ Cleaned {len(numeric_columns)} numeric columns")
    
    # Group and sum data
    print("\n" + "="*80)
    print("GROUPING DATA BY CATEGORY AND SUBCATEGORY")
    print("="*80)
    
    grouped_df = group_and_sum_data(df, numeric_columns)
    print(f"✓ Successfully grouped data into {len(grouped_df)} category-subcategory combinations")
    
    # Display analysis
    display_summary_stats(df, grouped_df)
    display_category_breakdown(grouped_df)
    display_top_subcategories(grouped_df)
    
    # Show sample of grouped data
    print("\n" + "="*80)
    print("SAMPLE GROUPED DATA (First 10 rows)")
    print("="*80)
    sample_columns = ['Category', 'Sub Category', 'Actual', 'Theoretical', 
                     'Variance Qty', 'Actual(incl.tax,etc)', 'Variance (incl.tax,etc)']
    print(grouped_df[sample_columns].head(10).to_string(index=False))
    
    # Save results
    save_grouped_data(grouped_df, output_file)
    
    print(f"\n" + "="*80)
    print("ANALYSIS COMPLETE!")
    print("="*80)
    print(f"Input file: {input_file}")
    print(f"Output file: {output_file}")
    print(f"Total groups created: {len(grouped_df)}")

if __name__ == "__main__":
    main()