#!/usr/bin/env python3
"""
Final diagnostic to confirm workarea mapping issue
"""

import pandas as pd

def confirm_workarea_mapping_issue():
    """Confirm that BAR workarea is being excluded from GROCERY calculation"""
    
    print("WORKAREA MAPPING DIAGNOSTIC - FINAL CONFIRMATION")
    print("="*80)
    
    # Load the CSV file
    df = pd.read_csv('inventory_consumption.csv')
    grocery_df = df[df['Sub Category'] == 'GROCERY'].copy()
    
    # Convert to numeric
    grocery_df['WorkArea Opening(incl.tax,etc)'] = pd.to_numeric(
        grocery_df['WorkArea Opening(incl.tax,etc)'], errors='coerce'
    ).fillna(0)
    
    print("GROCERY Opening Stock by WorkArea:")
    print("="*50)
    
    workarea_totals = grocery_df.groupby('WorkArea')['WorkArea Opening(incl.tax,etc)'].sum()
    total_all = 0
    
    for workarea, total in workarea_totals.items():
        total_all += total
        print(f"{workarea:15}: ₹{total:10,.2f}")
    
    print(f"{'TOTAL':15}: ₹{total_all:10,.2f}")
    
    print("\n" + "="*80)
    print("HYPOTHESIS TESTING")
    print("="*80)
    
    bar_total = workarea_totals.get('BAR', 0)
    without_bar = total_all - bar_total
    dashboard_value = 47372
    
    print(f"Total all workareas:     ₹{total_all:10,.2f}")
    print(f"BAR workarea only:       ₹{bar_total:10,.2f}")
    print(f"Without BAR:             ₹{without_bar:10,.2f}")
    print(f"Dashboard shows:         ₹{dashboard_value:10,.2f}")
    print(f"Difference (without BAR): ₹{abs(without_bar - dashboard_value):10,.2f}")
    
    if abs(without_bar - dashboard_value) < 100:
        print("\n✅ CONFIRMED: Dashboard is excluding BAR workarea from GROCERY!")
        print("   The difference is minimal, confirming our hypothesis.")
    else:
        print("\n❓ PARTIAL: BAR exclusion explains most but not all of the difference.")
    
    print("\n" + "="*80)
    print("ROOT CAUSE ANALYSIS")
    print("="*80)
    
    print("ISSUE IDENTIFIED:")
    print("• Dashboard category-workarea mappings exclude BAR from FOOD/GROCERY")
    print("• BAR workarea GROCERY items (₹3,390.11) are filtered out")
    print("• This causes dashboard to show ₹47,372 instead of ₹50,762.35")
    
    print("\nWHY THIS HAPPENS:")
    print("• dashboard_agents.py lines 365-382 check workarea mappings")
    print("• If GROCERY items in BAR don't match category-workarea config,")
    print("  they get moved to 'OTHERS' category or filtered out")
    
    print("\n" + "="*80)
    print("SOLUTION")
    print("="*80)
    
    print("FIX OPTIONS:")
    print("1. UPDATE CATEGORY-WORKAREA MAPPINGS:")
    print("   • Include BAR workarea in FOOD category mappings")
    print("   • This will include BAR GROCERY items in dashboard")
    
    print("\n2. MODIFY DASHBOARD LOGIC:")
    print("   • Remove workarea filtering for certain categories")
    print("   • Or ensure all workareas are included in mappings")
    
    print("\n3. CONFIGURATION CHECK:")
    print("   • Verify department_group_workarea_mappings includes BAR")
    print("   • Ensure FOOD category maps to all relevant workareas")
    
    print(f"\nEXPECTED RESULT AFTER FIX:")
    print(f"Dashboard should show: ₹{total_all:,.2f} (instead of ₹{dashboard_value:,.2f})")

if __name__ == "__main__":
    confirm_workarea_mapping_issue()