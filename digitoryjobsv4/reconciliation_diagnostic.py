#!/usr/bin/env python3
"""
Diagnostic script to identify reconciliation discrepancies between dashboard and grouped data
"""

import pandas as pd
import numpy as np

def diagnose_grocery_discrepancy():
    """Diagnose the GROCERY discrepancy between dashboard (47,372) and grouped data (50,762.35)"""
    
    print("RECONCILIATION DIAGNOSTIC - GROCERY DISCREPANCY ANALYSIS")
    print("="*80)
    
    # Load the CSV file
    df = pd.read_csv('inventory_consumption.csv')
    print(f"✓ Loaded {len(df)} total records")
    
    # Filter for GROCERY subcategory only (it's under FOOD category)
    grocery_df = df[df['Sub Category'] == 'GROCERY'].copy()
    print(f"✓ Found {len(grocery_df)} GROCERY records (Sub Category under FOOD)")
    
    if grocery_df.empty:
        print("✗ No GROCERY records found!")
        return
    
    print("\n" + "="*80)
    print("COLUMN ANALYSIS - All Value Columns for GROCERY")
    print("="*80)
    
    # Analyze all numeric columns
    numeric_columns = [
        'WorkArea Opening', 'WorkArea Indent', 'WorkArea Transfer In',
        'WorkArea Transfer Out', 'Return To Store Out', 'Spoilage/Adjustments',
        'Total WorkArea Stock', 'WorkArea Closing', 'Actual', 'Theoretical',
        'Variance Qty', 'WAC(incl.tax,etc)', 'WorkArea Opening(incl.tax,etc)',
        'WorkArea Closing(incl.tax,etc)', 'Actual(incl.tax,etc)',
        'Theoretical(incl.tax,etc)', 'Variance (incl.tax,etc)'
    ]
    
    for col in numeric_columns:
        if col in grocery_df.columns:
            # Convert to numeric and sum
            grocery_df[col] = pd.to_numeric(grocery_df[col], errors='coerce').fillna(0)
            total = grocery_df[col].sum()
            print(f"{col:35} = {total:15,.2f}")
    
    print("\n" + "="*80)
    print("DASHBOARD vs GROUPED DATA COMPARISON")
    print("="*80)
    
    # Dashboard value (from screenshot): 47,372
    dashboard_value = 47372
    actual_incl_tax = grocery_df['Actual(incl.tax,etc)'].sum()
    
    # Grouped data value (from screenshot): 50,762.35  
    grouped_value = 50762.35
    workarea_opening_incl_tax = grocery_df['WorkArea Opening(incl.tax,etc)'].sum()
    
    print(f"Dashboard shows GROCERY as:     {dashboard_value:15,.2f}")
    print(f"Actual(incl.tax,etc) sum:       {actual_incl_tax:15,.2f}")
    print(f"Match Dashboard?                {'✓ YES' if abs(actual_incl_tax - dashboard_value) < 1 else '✗ NO'}")
    
    print(f"\nGrouped data shows GROCERY as:  {grouped_value:15,.2f}")
    print(f"WorkArea Opening(incl.tax,etc): {workarea_opening_incl_tax:15,.2f}")
    print(f"Match Grouped?                  {'✓ YES' if abs(workarea_opening_incl_tax - grouped_value) < 1 else '✗ NO'}")
    
    print(f"\nDiscrepancy: {abs(actual_incl_tax - workarea_opening_incl_tax):15,.2f}")
    
    print("\n" + "="*80)
    print("ROOT CAUSE ANALYSIS")
    print("="*80)
    
    print("ISSUE IDENTIFIED:")
    print("• Dashboard is using 'Actual(incl.tax,etc)' column for consumption")
    print("• Grouped script is summing 'WorkArea Opening(incl.tax,etc)' column")
    print("• These are DIFFERENT metrics!")
    
    print("\nCOLUMN DEFINITIONS:")
    print("• Actual(incl.tax,etc): Actual consumption/usage value")
    print("• WorkArea Opening(incl.tax,etc): Opening stock value in work areas")
    
    print("\nRECONCILATION FORMULA ISSUE:")
    print("• Dashboard should use: Opening + Purchases + Transfers - Closing = Consumption")
    print("• But it's directly using 'Actual(incl.tax,etc)' instead of calculating")
    
    print("\n" + "="*80)
    print("DETAILED BREAKDOWN BY SUB CATEGORY")
    print("="*80)
    
    subcategory_analysis = grocery_df.groupby('Sub Category').agg({
        'Actual(incl.tax,etc)': 'sum',
        'WorkArea Opening(incl.tax,etc)': 'sum',
        'WorkArea Closing(incl.tax,etc)': 'sum',
        'WorkArea Indent': 'sum'
    }).round(2)
    
    subcategory_analysis['Difference'] = (
        subcategory_analysis['WorkArea Opening(incl.tax,etc)'] - 
        subcategory_analysis['Actual(incl.tax,etc)']
    )
    
    print(subcategory_analysis.to_string())
    
    print("\n" + "="*80)
    print("SOLUTION RECOMMENDATIONS")
    print("="*80)
    
    print("1. DASHBOARD FIX:")
    print("   • Update reconciliation dashboard to use proper reconciliation formula")
    print("   • Opening + Purchases + Transfers - Closing = Consumption")
    print("   • Don't directly use 'Actual(incl.tax,etc)' column")
    
    print("\n2. GROUPING SCRIPT FIX:")
    print("   • If you want consumption, use 'Actual(incl.tax,etc)' column")
    print("   • If you want opening stock, use 'WorkArea Opening(incl.tax,etc)' column")
    print("   • Make sure both dashboard and script use same metric")
    
    print("\n3. DATA VALIDATION:")
    print("   • Verify which column represents true consumption")
    print("   • Check if reconciliation formula gives same result as 'Actual(incl.tax,etc)'")
    
    # Calculate reconciliation formula
    print("\n" + "="*80)
    print("RECONCILIATION FORMULA TEST")
    print("="*80)
    
    opening = grocery_df['WorkArea Opening(incl.tax,etc)'].sum()
    closing = grocery_df['WorkArea Closing(incl.tax,etc)'].sum()
    indent = grocery_df['WorkArea Indent'].sum() * grocery_df['WAC(incl.tax,etc)'].mean()  # Approximate
    
    calculated_consumption = opening + indent - closing
    actual_consumption = grocery_df['Actual(incl.tax,etc)'].sum()
    
    print(f"Opening Stock:           {opening:15,.2f}")
    print(f"Closing Stock:           {closing:15,.2f}")
    print(f"Estimated Indent:        {indent:15,.2f}")
    print(f"Calculated Consumption:  {calculated_consumption:15,.2f}")
    print(f"Actual Consumption:      {actual_consumption:15,.2f}")
    print(f"Formula Accuracy:        {'✓ GOOD' if abs(calculated_consumption - actual_consumption) < 1000 else '✗ POOR'}")

if __name__ == "__main__":
    diagnose_grocery_discrepancy()