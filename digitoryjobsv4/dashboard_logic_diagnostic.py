#!/usr/bin/env python3
"""
Diagnostic script to trace dashboard logic for Opening Stock - Workareas calculation
Compares dashboard filtering vs simple grouping
"""

import pandas as pd

def trace_dashboard_logic():
    """Trace how dashboard_agents.py calculates opening_stock_kitchen for GROCERY"""
    
    print("DASHBOARD LOGIC DIAGNOSTIC - Opening Stock Workareas")
    print("="*80)
    
    # Load the CSV file
    df = pd.read_csv('inventory_consumption.csv')
    print(f"✓ Loaded {len(df)} total records")
    
    # Filter for GROCERY subcategory
    grocery_df = df[df['Sub Category'] == 'GROCERY'].copy()
    print(f"✓ Found {len(grocery_df)} GROCERY records")
    
    # Convert to numeric
    grocery_df['WorkArea Opening(incl.tax,etc)'] = pd.to_numeric(
        grocery_df['WorkArea Opening(incl.tax,etc)'], errors='coerce'
    ).fillna(0)
    
    print("\n" + "="*80)
    print("SIMPLE GROUPING (What we expect)")
    print("="*80)
    
    simple_total = grocery_df['WorkArea Opening(incl.tax,etc)'].sum()
    print(f"Simple sum of all GROCERY records: ₹{simple_total:,.2f}")
    
    print("\n" + "="*80)
    print("DASHBOARD FILTERING LOGIC SIMULATION")
    print("="*80)
    
    # Simulate dashboard filtering logic from dashboard_agents.py
    # The dashboard processes consumption_df with category/workarea mappings
    
    print("Dashboard applies these filters:")
    print("1. Category and subcategory validation")
    print("2. Workarea mapping validation") 
    print("3. Cross-category indent processing")
    
    # Check for invalid entries that dashboard might skip
    print("\n" + "="*50)
    print("RECORD VALIDATION CHECK")
    print("="*50)
    
    valid_records = 0
    invalid_records = 0
    valid_total = 0
    
    for _, row in grocery_df.iterrows():
        category = str(row.get('Category', 'Others')).strip()
        subcategory = str(row.get('Sub Category', 'Uncategorized')).strip()
        workarea = str(row.get('WorkArea', '')).strip()
        
        # Dashboard validation logic (from lines 359-362 in dashboard_agents.py)
        is_valid = True
        
        if not category or category == 'nan' or category == 'None':
            is_valid = False
        if not subcategory or subcategory == 'nan' or subcategory == 'None':
            is_valid = False
            
        if is_valid:
            valid_records += 1
            opening_value = float(row.get('WorkArea Opening(incl.tax,etc)', 0) or 0)
            valid_total += opening_value
        else:
            invalid_records += 1
    
    print(f"Valid records:     {valid_records}")
    print(f"Invalid records:   {invalid_records}")
    print(f"Valid total:       ₹{valid_total:,.2f}")
    
    print("\n" + "="*50)
    print("WORKAREA FILTERING CHECK")
    print("="*50)
    
    # Check if workarea filtering affects the result
    print("WorkArea values in GROCERY records:")
    workarea_counts = grocery_df['WorkArea'].value_counts()
    print(workarea_counts)
    
    # Check if any records have empty/null workareas
    empty_workarea = grocery_df[grocery_df['WorkArea'].isna() | (grocery_df['WorkArea'] == '')]
    if not empty_workarea.empty:
        empty_total = empty_workarea['WorkArea Opening(incl.tax,etc)'].sum()
        print(f"\nRecords with empty WorkArea: {len(empty_workarea)}")
        print(f"Their opening stock total: ₹{empty_total:,.2f}")
    
    print("\n" + "="*50)
    print("CATEGORY MAPPING SIMULATION")
    print("="*50)
    
    # The dashboard might be applying category-workarea mappings
    # that filter out some records
    
    # Simulate the mapping logic from dashboard_agents.py
    # Check if GROCERY/FOOD is in mapped categories
    
    print("Dashboard uses category_workarea_mappings to filter data")
    print("If GROCERY is not in the mappings, it goes to 'OTHERS' category")
    print("This could cause the discrepancy!")
    
    # Check for potential mapping issues
    print(f"\nAll GROCERY records by Location:")
    location_breakdown = grocery_df.groupby('Location')['WorkArea Opening(incl.tax,etc)'].sum()
    for location, total in location_breakdown.items():
        print(f"  {location}: ₹{total:,.2f}")
    
    print(f"\nAll GROCERY records by WorkArea:")
    workarea_breakdown = grocery_df.groupby('WorkArea')['WorkArea Opening(incl.tax,etc)'].sum()
    for workarea, total in workarea_breakdown.items():
        print(f"  {workarea}: ₹{total:,.2f}")
    
    print("\n" + "="*80)
    print("POTENTIAL CAUSES OF DISCREPANCY")
    print("="*80)
    
    print("Dashboard shows: ₹47,372")
    print(f"Simple grouping: ₹{simple_total:,.2f}")
    print(f"Difference: ₹{abs(simple_total - 47372):,.2f}")
    
    print("\nPossible causes:")
    print("1. Category-workarea mapping filters out some GROCERY records")
    print("2. Cross-category indent processing modifies values")
    print("3. Some records are moved to 'OTHERS' category")
    print("4. Workarea validation excludes certain records")
    
    # Calculate what portion would give us 47,372
    if simple_total > 0:
        percentage = (47372 / simple_total) * 100
        print(f"\n₹47,372 is {percentage:.1f}% of ₹{simple_total:,.2f}")
        print("This suggests some records are being filtered out")
    
    print("\n" + "="*80)
    print("RECOMMENDATION")
    print("="*80)
    print("To fix the discrepancy:")
    print("1. Check category_workarea_mappings configuration")
    print("2. Verify GROCERY/FOOD is properly mapped")
    print("3. Ensure all GROCERY records are included in reconciliation")
    print("4. Review workarea filtering logic in dashboard_agents.py")

if __name__ == "__main__":
    trace_dashboard_logic()